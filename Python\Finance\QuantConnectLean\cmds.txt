1. format C# code recursively(PowerShell)
  $clangFormat = "C:\Program Files\Microsoft Visual Studio\2022\Community\VC\Tools\Llvm\x64\bin\clang-format.exe"
  $targetRoot = "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\xOwn"
  Get-ChildItem -Path $targetRoot -Recurse -Filter *.cs | ForEach-Object {
      & "$clangFormat" -i -style=file $_.FullName
  }

2. install dotnet9
   # Ubuntu 24.04 LTS
    sudo add-apt-repository ppa:dotnet/backports
    sudo apt-get update && sudo apt-get install -y dotnet-sdk-9.0

   # Ubuntu 24.10
    sudo apt-get update && sudo apt-get install -y dotnet-sdk-9.0

3. build
  dotnet build "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\QuantConnect.Lean.sln" --configuration Debug

4. debug
  # mexc
  cd "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\Launcher\bin\Debug; .\QuantConnect.Lean.Launcher.exe --config D:\work\xstarwalker168\Python\Finance\QuantConnectLean\trading-bot-config.json --parameters tradeMode:2,symbol:SUI,currency:USDT,leverage:5 --results-destination-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir" --algorithm-language CSharp --environment live-mexc --algorithm-location cCryptoBot.dll --data-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data"

  # Binance futures
  cd "D:\work\xstarwalker168\Python\Finance\QuantConnectLean\TradingSolution\Lean\Launcher\bin\Debug; .\QuantConnect.Lean.Launcher.exe --config D:\work\xstarwalker168\Python\Finance\QuantConnectLean\trading-bot-config.json --parameters tradeMode:1,symbol:ETH,currency:USDC,leverage:5 --results-destination-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/QC-Log-Dir" --algorithm-language CSharp --environment live-futures-binance --algorithm-location cCryptoBot.dll --data-folder "D:/work/xstarwalker168/Python/Finance/QuantConnectLean/Data"

5. run
  cd /root/xQuantConnect/bin

  cd /root/xQuantConnect/bin && rm -rf ./*
  cd /root/xQuantConnect/bin && unzip Release.zip -d .

  # mexc
  cd /root/xQuantConnect/bin && screen -d -m dotnet /root/xQuantConnect/bin/QuantConnect.Lean.Launcher.dll --config /root/xQuantConnect/trading-bot-config.json --parameters tradeMode:2,symbol:SUI,currency:USDT,leverage:5 --results-destination-folder /root/xQuantConnect/QC-Log-Dir --algorithm-language CSharp --environment live-mexc --algorithm-location /root/xQuantConnect/bin/cCryptoBot.dll --data-folder /root/xQuantConnect/Data

  # binance
  cd /root/xQuantConnect/bin && screen -d -m dotnet /root/xQuantConnect/bin/QuantConnect.Lean.Launcher.dll --config /root/xQuantConnect/trading-bot-config.json --parameters tradeMode:1,symbol:ETH,currency:USDC,leverage:5 --results-destination-folder /root/xQuantConnect/QC-Log-Dir --algorithm-language CSharp --environment live-futures-binance --algorithm-location /root/xQuantConnect/bin/cCryptoBot.dll --data-folder /root/xQuantConnect/Data

  screen -X -S $(screen -ls | grep Detached | awk '{print $1}' | cut -d. -f1) quit

